{"name": "be-express-portfolio", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --build", "start": "node ./dist/index.js", "dev": "nodemon -p ./src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.11", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.4.0", "express-session": "^1.18.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/express": "^4.17.21", "nodemon": "^3.1.4", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}